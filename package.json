{"name": "ecosapiens-auto-bot", "version": "1.0.0", "description": "An automated bot for scanning products on the Ecosapiens platform with support for both canvas-generated and URL-based product images.", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/vikitoshi/Ecosapiens-Auto-Bot.git"}, "keywords": ["ecosapiens", "automation", "bot", "product-scanning", "canvas", "image-generation", "airdrop", "web3"], "author": "viki<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vikitoshi/Ecosapiens-Auto-Bot/issues"}, "homepage": "https://github.com/vikitoshi/Ecosapiens-Auto-Bot#readme", "dependencies": {"axios": "^1.9.0", "canvas": "^2.11.2", "dotenv": "^16.3.1", "form-data": "^4.0.0", "readline": "^1.3.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=14.0.0"}, "os": ["win32", "darwin", "linux"]}